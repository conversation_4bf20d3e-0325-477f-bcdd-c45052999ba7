package desk

import (
	"fmt"
	"fwyytool/libs/json"
	"fwyytool/service/desk"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

var DataDiffController dataDiffController

type dataDiffController struct {
}

// DataDiffDetailParams 数据差异查询参数
type DataDiffDetailParams struct {
	TimeRange int64  `json:"timeRange" form:"timeRange" binding:"required,min=1,max=30"`
	Handler   string `json:"handler" form:"handler" binding:"required"`
}

// Validate 验证参数
func (p *DataDiffDetailParams) Validate() error {
	// 验证时间范围
	if p.TimeRange < 1 || p.TimeRange > 30 {
		return fmt.Errorf("时间范围必须在1-30天之间，当前值: %d", p.TimeRange)
	}

	// 验证接口名称
	if strings.TrimSpace(p.Handler) == "" {
		return fmt.Errorf("接口名称不能为空")
	}

	// 验证接口名称格式
	if !strings.Contains(p.Handler, "_") {
		return fmt.Errorf("接口名称格式不正确，应包含下划线，如: StudentCallInfo_0")
	}

	parts := strings.Split(p.Handler, "_")
	if len(parts) != 2 {
		return fmt.Errorf("接口名称格式不正确，应为: 接口名_类型，如: StudentCallInfo_0")
	}

	// 验证类型后缀
	suffix := parts[1]
	if suffix != "0" && suffix != "1" {
		return fmt.Errorf("接口类型后缀只能是0(新老diff)或1(环比diff)，当前值: %s", suffix)
	}

	return nil
}

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
	Time    string `json:"time"`
}

// DataDiffDetail 数据差异详情页面处理器
func (s dataDiffController) DataDiffDetail(ctx *gin.Context) {
	startTime := time.Now()

	// 初始化响应数据
	var params DataDiffDetailParams
	var errMsg string
	var errorDetails []string

	// 参数绑定和验证
	if err := ctx.ShouldBind(&params); err != nil {
		errMsg = "参数绑定失败"
		errorDetails = append(errorDetails, fmt.Sprintf("绑定错误: %v", err))

		// 尝试解析具体的验证错误
		if strings.Contains(err.Error(), "required") {
			errorDetails = append(errorDetails, "必填参数缺失")
		}
		if strings.Contains(err.Error(), "min") || strings.Contains(err.Error(), "max") {
			errorDetails = append(errorDetails, "参数值超出允许范围")
		}
	} else {
		// 自定义验证
		if validationErr := params.Validate(); validationErr != nil {
			errMsg = "参数验证失败"
			errorDetails = append(errorDetails, validationErr.Error())
		}
	}

	// 如果有参数错误，记录并返回
	if errMsg != "" {
		s.renderErrorResponse(ctx, params, errMsg, errorDetails, startTime)
		return
	}

	// 调用服务获取数据
	data, err := desk.ArkDataDiffService.GetDiffRes(ctx, params.TimeRange, params.Handler)
	if err != nil {
		errMsg = "获取数据失败"
		errorDetails = append(errorDetails, fmt.Sprintf("服务调用错误: %v", err))

		// 根据错误类型提供更详细的信息
		if strings.Contains(err.Error(), "timeout") {
			errorDetails = append(errorDetails, "请求超时，请稍后重试")
		} else if strings.Contains(err.Error(), "connection") {
			errorDetails = append(errorDetails, "网络连接失败，请检查网络状态")
		} else if strings.Contains(err.Error(), "not found") {
			errorDetails = append(errorDetails, "未找到指定的接口数据")
		} else if strings.Contains(err.Error(), "unauthorized") {
			errorDetails = append(errorDetails, "权限不足，请联系管理员")
		}

		s.renderErrorResponse(ctx, params, errMsg, errorDetails, startTime)
		return
	}

	// 序列化数据
	dataJson, jsonErr := json.MarshalToString(data)
	if jsonErr != nil {
		errMsg = "数据序列化失败"
		errorDetails = append(errorDetails, fmt.Sprintf("JSON序列化错误: %v", jsonErr))
		s.renderErrorResponse(ctx, params, errMsg, errorDetails, startTime)
		return
	}

	// 成功响应
	s.renderSuccessResponse(ctx, data, dataJson, params, startTime)
}

// renderErrorResponse 渲染错误响应
func (s dataDiffController) renderErrorResponse(ctx *gin.Context, params DataDiffDetailParams, errMsg string, errorDetails []string, startTime time.Time) {
	// 记录错误日志
	ctx.Header("X-Request-ID", fmt.Sprintf("diff_%d", time.Now().UnixNano()))

	// 构建详细错误信息
	detailsStr := strings.Join(errorDetails, "; ")

	output := gin.H{
		"data":         nil,
		"dataJson":     "{}",
		"errMsg":       errMsg,
		"errorDetails": detailsStr,
		"params":       params,
		"timestamp":    time.Now().Unix(),
		"requestTime":  time.Since(startTime).Milliseconds(),
		"suggestions":  s.getErrorSuggestions(errMsg),
	}

	ctx.HTML(http.StatusOK, "desk/datadiff/diffdetail.html", output)
}

// renderSuccessResponse 渲染成功响应
func (s dataDiffController) renderSuccessResponse(ctx *gin.Context, data interface{}, dataJson string, params DataDiffDetailParams, startTime time.Time) {
	ctx.Header("X-Request-ID", fmt.Sprintf("diff_%d", time.Now().UnixNano()))

	output := gin.H{
		"data":        data,
		"dataJson":    dataJson,
		"errMsg":      "",
		"params":      params,
		"timestamp":   time.Now().Unix(),
		"requestTime": time.Since(startTime).Milliseconds(),
		"success":     true,
	}

	ctx.HTML(http.StatusOK, "desk/datadiff/diffdetail.html", output)
}

// getErrorSuggestions 获取错误建议
func (s dataDiffController) getErrorSuggestions(errMsg string) []string {
	suggestions := []string{}

	switch {
	case strings.Contains(errMsg, "参数"):
		suggestions = append(suggestions, "请检查时间范围是否在1-30天之间")
		suggestions = append(suggestions, "请确保接口名称格式正确，如: StudentCallInfo_0")
		suggestions = append(suggestions, "请确认接口类型后缀为0或1")
	case strings.Contains(errMsg, "获取数据"):
		suggestions = append(suggestions, "请稍后重试")
		suggestions = append(suggestions, "如果问题持续存在，请联系技术支持")
		suggestions = append(suggestions, "可以尝试减少时间范围或更换接口")
	case strings.Contains(errMsg, "序列化"):
		suggestions = append(suggestions, "数据格式异常，请联系技术支持")
	default:
		suggestions = append(suggestions, "请刷新页面重试")
		suggestions = append(suggestions, "如果问题持续存在，请联系技术支持")
	}

	return suggestions
}

@charset "utf-8";
/*
 * 数据差异页面增强样式
 * 提供现代化的页面布局和响应式设计
 */

/* ===== 页面整体布局优化 ===== */
.datadiff-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
    min-height: 100vh;
}

.datadiff-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.datadiff-header h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

/* ===== 表单样式优化 ===== */
.datadiff-form {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.datadiff-form .form-group {
    margin-bottom: 20px;
}

.datadiff-form label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.datadiff-form .form-control {
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 10px 15px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.datadiff-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

.datadiff-form .btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    font-weight: 600;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.datadiff-form .btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

/* ===== 统计卡片样式 ===== */
.stats-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.stats-table {
    width: 100%;
    margin: 0;
    border-collapse: collapse;
}

.stats-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
    border: none;
}

.stats-table td {
    padding: 20px;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    border: none;
    background: white;
}

.stats-table .stat-total {
    color: #28a745;
}

.stats-table .stat-diff {
    color: #dc3545;
}

.stats-table .stat-no-diff {
    color: #28a745;
}

.stats-table .stat-unfinished {
    color: #ffc107;
}

/* ===== 昨日小计样式 ===== */
.yesterday-summary {
    margin-bottom: 20px;
}

.yesterday-summary-link {
    display: inline-block;
    color: #dc3545 !important;
    font-size: 18px;
    font-weight: bold;
    text-decoration: none;
    padding: 10px 20px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.yesterday-summary-link:hover {
    color: #c82333 !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.yesterday-data {
    margin-top: 15px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: none;
}

/* ===== 标签页样式优化 ===== */
.datadiff-tabs {
    background: white;
    border-radius: 8px 8px 0 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 0;
}

.datadiff-tabs .nav-tabs {
    border-bottom: none;
    margin: 0;
}

.datadiff-tabs .nav-tabs > li {
    margin-bottom: 0;
}

.datadiff-tabs .nav-tabs > li > a {
    border: none;
    border-radius: 0;
    padding: 15px 25px;
    color: #495057;
    font-weight: 600;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.datadiff-tabs .nav-tabs > li.active > a {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: 3px solid #28a745;
}

/* ===== 数据表格样式优化 ===== */
.datadiff-table-container {
    background: white;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.datadiff-table {
    width: 100%;
    margin: 0;
    border-collapse: collapse;
    font-size: 14px;
}

.datadiff-table th {
    background: linear-gradient(135deg, #495057 0%, #6c757d 100%);
    color: white;
    padding: 15px 10px;
    text-align: center;
    font-weight: 600;
    border: none;
    position: sticky;
    top: 0;
    z-index: 10;
}

.datadiff-table td {
    padding: 15px 10px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: top;
    background: white;
}

.datadiff-table tr:hover td {
    background-color: #f8f9fa;
}

/* ===== JSON文本域样式优化 ===== */
.json-textarea {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    background-color: #f8f9fa;
    resize: both;
    min-width: 200px;
    min-height: 100px;
    max-width: 100%;
    width: 100%;
    height: 200px;
    box-sizing: border-box;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.json-textarea:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
    background-color: white;
}

/* ===== 差异链接样式 ===== */
.diff-link {
    color: #dc3545 !important;
    font-weight: 600;
    text-decoration: none;
    padding: 6px 12px;
    background: #fff5f5;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.diff-link:hover {
    color: #c82333 !important;
    background: #f8d7da;
    border-color: #f1b0b7;
    text-decoration: none;
}

/* ===== 响应式设计 ===== */
@media (max-width: 1200px) {
    .datadiff-container {
        padding: 15px;
    }
    
    .datadiff-table th,
    .datadiff-table td {
        padding: 10px 8px;
        font-size: 13px;
    }
    
    .json-textarea {
        height: 150px;
        min-width: 150px;
    }
}

@media (max-width: 768px) {
    .datadiff-container {
        padding: 10px;
    }
    
    .datadiff-header {
        padding: 15px;
    }
    
    .datadiff-header h3 {
        font-size: 20px;
    }
    
    .datadiff-form {
        padding: 20px;
    }
    
    .datadiff-table-container {
        overflow-x: auto;
    }
    
    .datadiff-table {
        min-width: 800px;
    }
    
    .json-textarea {
        height: 120px;
        min-width: 120px;
        font-size: 11px;
    }
}

@media (max-width: 480px) {
    .datadiff-container {
        padding: 5px;
    }
    
    .datadiff-header,
    .datadiff-form {
        padding: 15px;
    }
    
    .stats-table th,
    .stats-table td {
        padding: 10px 5px;
        font-size: 12px;
    }
    
    .datadiff-table th,
    .datadiff-table td {
        padding: 8px 5px;
        font-size: 12px;
    }
}

/* ===== 加载状态样式 ===== */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== 错误提示样式 ===== */
.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 12px 20px;
    border-radius: 6px;
    border: 1px solid #f5c6cb;
    margin: 10px 0;
    font-weight: 500;
}

.success-message {
    background: #d4edda;
    color: #155724;
    padding: 12px 20px;
    border-radius: 6px;
    border: 1px solid #c3e6cb;
    margin: 10px 0;
    font-weight: 500;
}

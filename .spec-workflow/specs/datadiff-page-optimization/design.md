# Design Document

## Overview

本设计文档描述了数据差异对比页面（`templates/desk/datadiff/diffdetail.html`）的优化方案。该页面是一个用于展示API接口新老版本数据差异对比结果的工具页面，优化目标是提升用户体验、改善数据展示效果和增强交互功能。

优化将遵循项目现有的技术栈和架构模式，包括：
- Go Gin框架 + HTML模板系统
- Bootstrap CSS框架
- jQuery + 原生JavaScript
- 现有的静态资源组织结构

## Steering Document Alignment

### Technical Standards (tech.md)
由于项目暂无tech.md文档，设计将遵循以下技术标准：
- 使用项目现有的技术栈（Bootstrap + jQuery）
- 遵循现有的静态资源组织方式（/assets目录结构）
- 保持与现有页面的设计一致性
- 使用项目已有的CSS类和JavaScript模式

### Project Structure (structure.md)
由于项目暂无structure.md文档，设计将遵循现有的项目结构：
- 模板文件位于 `templates/` 目录
- 静态资源位于 `assets/` 目录
- CSS文件位于 `assets/css/`
- JavaScript文件位于 `assets/js/`
- 控制器位于 `controllers/http/desk/`

## Code Reuse Analysis

### Existing Components to Leverage
- **Bootstrap CSS Framework**: 项目已使用bootstrap.min.css，将继续使用其组件和样式
- **jQuery Library**: 项目已引入jquery-2.1.4.min.js，将用于DOM操作和AJAX请求
- **Layer.js**: 项目已使用layer.js弹层组件，将用于模态框和提示
- **Template Functions**: 使用现有的模板函数如`jsonPrettyStr`、`showTime`等
- **Existing CSS Patterns**: 复用现有的表格样式、按钮样式等

### Integration Points
- **DataDiffController**: 与现有的`controllers/http/desk/dataDiff.go`控制器集成
- **Template System**: 使用Gin的HTML模板系统和现有的模板函数
- **Static Assets**: 集成到现有的静态资源管理系统
- **API Endpoints**: 复用现有的`/arkgo/tool/getdiffcount`等API接口

## Architecture

整体架构采用传统的MVC模式，保持与项目现有架构的一致性：

### Modular Design Principles
- **Single File Responsibility**: 将CSS、JavaScript和HTML模板分离
- **Component Isolation**: 创建独立的CSS模块和JavaScript模块
- **Service Layer Separation**: 保持控制器、服务层和模板的分离
- **Utility Modularity**: 创建可复用的工具函数和样式组件

```mermaid
graph TD
    A[diffdetail.html Template] --> B[Enhanced CSS Styles]
    A --> C[Enhanced JavaScript Modules]
    A --> D[DataDiffController]
    
    B --> E[datadiff-enhanced.css]
    B --> F[json-viewer.css]
    
    C --> G[datadiff-enhanced.js]
    C --> H[json-formatter.js]
    C --> I[table-enhancer.js]
    
    D --> J[ArkDataDiffService]
    D --> K[Template Functions]
    
    E --> L[Bootstrap Base Styles]
    G --> M[jQuery Library]
    G --> N[Layer.js]
```

## Components and Interfaces

### Component 1: Enhanced CSS Styles
- **Purpose:** 提供现代化的样式和响应式布局
- **Files:** `assets/css/datadiff-enhanced.css`, `assets/css/json-viewer.css`
- **Interfaces:** CSS类和样式规则
- **Dependencies:** Bootstrap CSS框架
- **Reuses:** 现有的Bootstrap组件和项目CSS规范

### Component 2: JSON Data Formatter
- **Purpose:** 格式化和高亮显示JSON数据
- **Files:** `assets/js/json-formatter.js`
- **Interfaces:** 
  - `formatJson(jsonString, container)` - 格式化JSON并显示
  - `highlightDifferences(oldJson, newJson)` - 高亮差异
- **Dependencies:** jQuery
- **Reuses:** 现有的`tool.js`中的JSON处理逻辑

### Component 3: Table Enhancer
- **Purpose:** 增强表格功能，包括排序、搜索、分页
- **Files:** `assets/js/table-enhancer.js`
- **Interfaces:**
  - `initTableSort(tableSelector)` - 初始化表格排序
  - `addTableSearch(tableSelector, searchInputSelector)` - 添加搜索功能
  - `initTablePagination(tableSelector, pageSize)` - 初始化分页
- **Dependencies:** jQuery, Bootstrap
- **Reuses:** 现有的表格样式和Bootstrap组件

### Component 4: Data Loading Manager
- **Purpose:** 管理数据加载状态和错误处理
- **Files:** `assets/js/datadiff-enhanced.js`
- **Interfaces:**
  - `showLoadingState(container)` - 显示加载状态
  - `hideLoadingState(container)` - 隐藏加载状态
  - `handleError(error, container)` - 处理错误显示
- **Dependencies:** jQuery, Layer.js
- **Reuses:** 现有的Layer.js弹层组件

### Component 5: Enhanced Template
- **Purpose:** 优化后的HTML模板结构
- **Files:** `templates/desk/datadiff/diffdetail.html`
- **Interfaces:** HTML模板和Go模板语法
- **Dependencies:** Gin模板系统
- **Reuses:** 现有的模板函数和数据结构

## Data Models

### DiffDetailPageData
```go
type DiffDetailPageData struct {
    Data     arkgo.GetArkStudentDataDiffResp `json:"data"`
    DataJson string                          `json:"dataJson"`
    ErrMsg   string                          `json:"errMsg"`
    Params   struct {
        TimeRange int64  `json:"timeRange"`
        Handler   string `json:"handler"`
    } `json:"params"`
}
```

### Enhanced Frontend Data Models
```javascript
// 表格配置
const TableConfig = {
    pageSize: 20,
    sortable: true,
    searchable: true,
    columns: [
        { key: 'params', title: '请求参数', sortable: false },
        { key: 'diffNum', title: 'diff数', sortable: true },
        { key: 'oldData', title: 'before返回值', sortable: false },
        { key: 'newData', title: 'new返回值', sortable: false },
        { key: 'diffResult', title: 'diff详情', sortable: false },
        { key: 'handlerName', title: '接口名', sortable: true },
        { key: 'updateTime', title: '更新时间', sortable: true },
        { key: 'createTime', title: '创建时间', sortable: true }
    ]
};

// JSON查看器配置
const JsonViewerConfig = {
    theme: 'default',
    collapsed: 2,
    withQuotes: true,
    withLinks: false
};
```

## Error Handling

### Error Scenarios
1. **数据加载失败**
   - **Handling:** 显示友好的错误提示，提供重试按钮
   - **User Impact:** 用户看到明确的错误信息和解决建议

2. **JSON解析错误**
   - **Handling:** 降级显示原始文本，记录错误日志
   - **User Impact:** 数据仍可查看，但无语法高亮

3. **网络请求超时**
   - **Handling:** 显示超时提示，自动重试机制
   - **User Impact:** 用户获得明确的状态反馈

4. **大数据量渲染性能问题**
   - **Handling:** 实现虚拟滚动或分页加载
   - **User Impact:** 页面保持响应性

## Testing Strategy

### Unit Testing
- JSON格式化函数的单元测试
- 表格排序和搜索功能测试
- 错误处理逻辑测试
- CSS样式在不同浏览器的兼容性测试

### Integration Testing
- 与现有API接口的集成测试
- 模板渲染和数据绑定测试
- 静态资源加载测试
- 跨浏览器兼容性测试

### End-to-End Testing
- 完整的用户操作流程测试
- 数据查询和展示功能测试
- 响应式布局在不同设备上的测试
- 性能测试（大数据量场景）

## Implementation Plan

### Phase 1: CSS样式优化
1. 创建`assets/css/datadiff-enhanced.css`
2. 创建`assets/css/json-viewer.css`
3. 优化响应式布局和现代化样式

### Phase 2: JavaScript功能增强
1. 创建`assets/js/json-formatter.js`
2. 创建`assets/js/table-enhancer.js`
3. 创建`assets/js/datadiff-enhanced.js`
4. 实现数据格式化、表格增强和交互优化

### Phase 3: 模板结构优化
1. 重构`templates/desk/datadiff/diffdetail.html`
2. 集成新的CSS和JavaScript组件
3. 优化HTML结构和语义化

### Phase 4: 测试和优化
1. 进行跨浏览器测试
2. 性能优化和调试
3. 用户体验测试和反馈收集

## Performance Considerations

- **CSS优化**: 使用CSS压缩和合并，避免重复样式
- **JavaScript优化**: 实现防抖和节流，优化DOM操作
- **数据渲染**: 对大量数据实现虚拟滚动或分页
- **资源加载**: 使用CDN和缓存策略优化静态资源加载
- **内存管理**: 及时清理事件监听器和DOM引用